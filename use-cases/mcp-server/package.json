{"name": "remote-mcp-github-oauth", "version": "0.0.1", "private": true, "type": "module", "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "cf-typegen": "wrangler types", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "dependencies": {"@cloudflare/workers-oauth-provider": "^0.0.5", "@modelcontextprotocol/sdk": "1.13.1", "@sentry/cloudflare": "^9.16.0", "agents": "^0.0.100", "hono": "^4.8.3", "just-pick": "^4.2.0", "octokit": "^5.0.3", "postgres": "^3.4.5", "workers-mcp": "^0.0.13", "zod": "^3.25.67"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.53", "@types/node": "^24.0.10", "@vitest/ui": "^3.2.4", "prettier": "^3.6.2", "typescript": "^5.8.3", "vi-fetch": "^0.8.0", "vitest": "^3.2.4", "wrangler": "^4.23.0"}}